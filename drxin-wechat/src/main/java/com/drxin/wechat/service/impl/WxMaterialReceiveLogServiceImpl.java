package com.drxin.wechat.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.drxin.common.exception.ServiceException;
import com.drxin.common.utils.StringUtils;
import com.drxin.wechat.domain.WxMaterialReceiveLog;
import com.drxin.wechat.mapper.WxMaterialReceiveLogMapper;
import com.drxin.wechat.service.IWxMaterialReceiveLogService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.Date;
import java.util.List;

/**
 * 微信物料领取记录Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-07-27
 */
@Service
public class WxMaterialReceiveLogServiceImpl extends ServiceImpl<WxMaterialReceiveLogMapper, WxMaterialReceiveLog> implements IWxMaterialReceiveLogService {

    @Resource
    private WxMaterialReceiveLogMapper wxMaterialReceiveLogMapper;

    /**
     * 用户申请领取物料
     * 
     * @param userId 用户ID
     * @param materialId 物料ID
     * @return 申请结果消息
     */
    @Override
    public String applyMaterial(Long userId, Long materialId) {
        // 1. 校验物料是否存在（通过查询material_info表）
        if (!isMaterialExists(materialId)) {
            throw new ServiceException("物料不存在");
        }
        
        // 2. 校验是否已经领取过该物料
        if (hasUserReceivedMaterial(userId, materialId)) {
            throw new ServiceException("您已经领取过该物料");
        }
        
        // 3. 查找待领取的记录
        WxMaterialReceiveLog existingRecord = findPendingRecord(userId, materialId);
        
        if (existingRecord == null) {
            throw new ServiceException("未找到待领取的记录");
        }
        
        // 4. 更新状态为申请中，记录申请时间
        existingRecord.setStatus("applying");
        existingRecord.setApplyTime(new Date());
        boolean result = this.updateById(existingRecord);

        if (!result) {
            throw new ServiceException("申请提交失败");
        }
        
        return "申请提交成功，等待管理员确认";
    }

    /**
     * 查询用户的物料领取记录列表
     * 
     * @param userId 用户ID
     * @return 用户的领取记录列表
     */
    @Override
    public List<WxMaterialReceiveLog> selectUserMaterialReceiveList(Long userId) {
        return wxMaterialReceiveLogMapper.selectUserMaterialReceiveList(userId);
    }

    /**
     * 检查用户是否已经领取过指定物料
     *
     * @param userId 用户ID
     * @param materialId 物料ID
     * @return 是否已领取
     */
    @Override
    public boolean hasUserReceivedMaterial(Long userId, Long materialId) {
        QueryWrapper<WxMaterialReceiveLog> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("user_id", userId)
                   .eq("material_id", materialId)
                   .eq("status", "completed");
        return this.count(queryWrapper) > 0;
    }

    /**
     * 验证用户身份是否有权限申请物料
     * 
     * @param userType 用户类型
     * @return 是否有权限
     */
    @Override
    public boolean validateUserIdentity(String userType) {
        if (StringUtils.isEmpty(userType)) {
            return false;
        }
        
        // 支持的身份类型：急救员、导师、弟子
        List<String> allowedTypes = Arrays.asList("aider", "mentor", "disciple");
        
        // 用户可能有多个身份，用逗号分隔
        String[] userTypes = userType.split(",");
        return Arrays.stream(userTypes)
                .anyMatch(type -> allowedTypes.contains(type.trim()));
    }

    /**
     * 检查物料是否存在
     * 
     * @param materialId 物料ID
     * @return 是否存在
     */
    private boolean isMaterialExists(Long materialId) {
        return wxMaterialReceiveLogMapper.countMaterialById(materialId) > 0;
    }

    /**
     * 查找待领取的记录
     *
     * @param userId 用户ID
     * @param materialId 物料ID
     * @return 待领取的记录
     */
    private WxMaterialReceiveLog findPendingRecord(Long userId, Long materialId) {
        QueryWrapper<WxMaterialReceiveLog> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("user_id", userId)
                   .eq("material_id", materialId)
                   .eq("status", "pending")
                   .last("LIMIT 1");
        return this.getOne(queryWrapper);
    }

}
