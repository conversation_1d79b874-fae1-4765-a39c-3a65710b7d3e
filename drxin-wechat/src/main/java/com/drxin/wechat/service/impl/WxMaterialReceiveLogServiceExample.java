package com.drxin.wechat.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.drxin.wechat.domain.WxMaterialReceiveLog;
import com.drxin.wechat.service.IWxMaterialReceiveLogService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Arrays;
import java.util.Date;
import java.util.List;

/**
 * MyBatis Plus功能使用示例
 * 
 * <AUTHOR>
 * @date 2025-07-27
 */
@Component
public class WxMaterialReceiveLogServiceExample {

    @Autowired
    private IWxMaterialReceiveLogService wxMaterialReceiveLogService;

    /**
     * MyBatis Plus基础CRUD操作示例
     */
    public void basicCrudExample() {
        // 1. 新增记录
        WxMaterialReceiveLog newRecord = new WxMaterialReceiveLog()
                .setUserId(1001L)
                .setMaterialId(2001L)
                .setMaterialName("急救包")
                .setQuantity(1L)
                .setStatus("pending")
                .setTriggerType("auto");
        
        boolean saveResult = wxMaterialReceiveLogService.save(newRecord);
        System.out.println("保存结果: " + saveResult);

        // 2. 根据ID查询
        WxMaterialReceiveLog record = wxMaterialReceiveLogService.getById(newRecord.getRecordId());
        System.out.println("查询记录: " + record);

        // 3. 更新记录
        record.setStatus("applying").setApplyTime(new Date());
        boolean updateResult = wxMaterialReceiveLogService.updateById(record);
        System.out.println("更新结果: " + updateResult);

        // 4. 删除记录
        boolean deleteResult = wxMaterialReceiveLogService.removeById(record.getRecordId());
        System.out.println("删除结果: " + deleteResult);
    }

    /**
     * 条件查询示例
     */
    public void queryWrapperExample() {
        // 1. 基础条件查询
        QueryWrapper<WxMaterialReceiveLog> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("user_id", 1001L)
                   .eq("status", "pending")
                   .orderByDesc("create_time");
        
        List<WxMaterialReceiveLog> records = wxMaterialReceiveLogService.list(queryWrapper);
        System.out.println("条件查询结果: " + records.size());

        // 2. 复杂条件查询
        QueryWrapper<WxMaterialReceiveLog> complexQuery = new QueryWrapper<>();
        complexQuery.eq("user_id", 1001L)
                   .in("status", Arrays.asList("pending", "applying"))
                   .isNotNull("material_id")
                   .ge("create_time", "2025-01-01")
                   .orderByDesc("create_time")
                   .last("LIMIT 10");
        
        List<WxMaterialReceiveLog> complexRecords = wxMaterialReceiveLogService.list(complexQuery);
        System.out.println("复杂查询结果: " + complexRecords.size());

        // 3. 统计查询
        QueryWrapper<WxMaterialReceiveLog> countQuery = new QueryWrapper<>();
        countQuery.eq("user_id", 1001L).eq("status", "completed");
        long completedCount = wxMaterialReceiveLogService.count(countQuery);
        System.out.println("已完成数量: " + completedCount);
    }

    /**
     * 批量操作示例
     */
    public void batchOperationExample() {
        // 1. 批量新增
        List<WxMaterialReceiveLog> batchRecords = Arrays.asList(
            new WxMaterialReceiveLog().setUserId(1001L).setMaterialId(2001L).setStatus("pending"),
            new WxMaterialReceiveLog().setUserId(1002L).setMaterialId(2002L).setStatus("pending"),
            new WxMaterialReceiveLog().setUserId(1003L).setMaterialId(2003L).setStatus("pending")
        );
        
        boolean batchSaveResult = wxMaterialReceiveLogService.saveBatch(batchRecords);
        System.out.println("批量保存结果: " + batchSaveResult);

        // 2. 批量更新
        UpdateWrapper<WxMaterialReceiveLog> updateWrapper = new UpdateWrapper<>();
        updateWrapper.in("user_id", Arrays.asList(1001L, 1002L, 1003L))
                    .eq("status", "pending")
                    .set("status", "applying")
                    .set("apply_time", new Date());
        
        boolean batchUpdateResult = wxMaterialReceiveLogService.update(updateWrapper);
        System.out.println("批量更新结果: " + batchUpdateResult);

        // 3. 批量删除
        QueryWrapper<WxMaterialReceiveLog> deleteQuery = new QueryWrapper<>();
        deleteQuery.in("user_id", Arrays.asList(1001L, 1002L, 1003L));
        
        boolean batchDeleteResult = wxMaterialReceiveLogService.remove(deleteQuery);
        System.out.println("批量删除结果: " + batchDeleteResult);
    }

    /**
     * 分页查询示例
     */
    public void pageQueryExample() {
        // 创建分页对象
        Page<WxMaterialReceiveLog> page = new Page<>(1, 10); // 第1页，每页10条

        // 创建查询条件
        QueryWrapper<WxMaterialReceiveLog> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("status", "pending")
                   .orderByDesc("create_time");

        // 执行分页查询
        Page<WxMaterialReceiveLog> resultPage = wxMaterialReceiveLogService.page(page, queryWrapper);

        System.out.println("总记录数: " + resultPage.getTotal());
        System.out.println("总页数: " + resultPage.getPages());
        System.out.println("当前页: " + resultPage.getCurrent());
        System.out.println("每页大小: " + resultPage.getSize());
        System.out.println("当前页记录: " + resultPage.getRecords().size());
    }

    /**
     * 业务方法使用示例
     */
    public void businessMethodExample() {
        Long userId = 1001L;
        Long materialId = 2001L;

        // 1. 使用业务方法
        boolean hasReceived = wxMaterialReceiveLogService.hasUserReceivedMaterial(userId, materialId);
        System.out.println("用户是否已领取: " + hasReceived);

        // 2. 获取用户记录
        List<WxMaterialReceiveLog> userRecords = wxMaterialReceiveLogService.selectUserMaterialReceiveList(userId);
        System.out.println("用户记录数: " + userRecords.size());

        // 3. 根据状态查询
        List<WxMaterialReceiveLog> pendingRecords = wxMaterialReceiveLogService.listByStatus("pending");
        System.out.println("待处理记录数: " + pendingRecords.size());

        // 4. 批量更新状态
        List<Long> recordIds = Arrays.asList(1L, 2L, 3L);
        boolean batchUpdateResult = wxMaterialReceiveLogService.batchUpdateStatus(recordIds, "completed");
        System.out.println("批量更新状态结果: " + batchUpdateResult);
    }
}
