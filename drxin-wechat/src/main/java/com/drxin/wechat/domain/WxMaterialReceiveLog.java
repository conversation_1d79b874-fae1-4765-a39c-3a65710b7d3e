package com.drxin.wechat.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;

/**
 * 物料领取记录对象 material_receive_log
 * 
 * <AUTHOR>
 * @date 2025-07-27
 */
@Data
@Accessors(chain = true)
@TableName("material_receive_log")
public class WxMaterialReceiveLog implements Serializable {
    private static final long serialVersionUID = 1L;

    /** 领取记录ID */
    @TableId(value = "record_id", type = IdType.AUTO)
    private Long recordId;

    /** 领取用户ID */
    private Long userId;

    /** 用户姓名（冗余字段） */
    private String userName;

    /** 物料ID */
    private Long materialId;

    /** 物料名称（冗余字段） */
    private String materialName;

    /** 领取数量，默认1 */
    private Long quantity;

    /** 领取状态：pending=待领取，applying=申请中，completed=已领取 */
    private String status;

    /** 记录创建方式：auto=系统自动生成，manual=手动创建 */
    private String triggerType;

    /** 用户点击确认申请的时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date applyTime;

    /** 管理员确认发放的时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date confirmTime;

    /** 处理人ID */
    private Long handlerId;

    /** 处理人姓名 */
    private String handlerName;

    /** 备注 */
    private String remark;

    /** 创建时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @TableField("create_time")
    private Date createTime;

    /** 更新时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @TableField("update_time")
    private Date updateTime;
}
