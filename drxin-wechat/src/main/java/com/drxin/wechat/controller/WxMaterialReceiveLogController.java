package com.drxin.wechat.controller;

import com.drxin.common.core.controller.BaseController;
import com.drxin.common.core.domain.R;
import com.drxin.common.core.domain.entity.SysUser;
import com.drxin.common.core.domain.model.LoginUser;
import com.drxin.common.exception.ServiceException;
import com.drxin.common.utils.SecurityUtils;
import com.drxin.wechat.domain.WxMaterialReceiveLog;
import com.drxin.wechat.service.IWxMaterialReceiveLogService;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * 物料领取记录Controller
 * 
 * <AUTHOR>
 * @date 2025-07-27
 */
@RestController
@RequestMapping("/wx/material_receive_log")
public class WxMaterialReceiveLogController extends BaseController {

    @Resource
    private IWxMaterialReceiveLogService wxMaterialReceiveLogService;

    /**
     * 用户提交领取申请
     *
     * @param userId 用户ID
     * @param materialId 物料ID
     * @return 申请结果
     */
    @PostMapping("/apply")
    public R<String> applyMaterial(@RequestParam Long userId, @RequestParam Long materialId) {
        try {
            // 1. 校验用户身份
            LoginUser loginUser = SecurityUtils.getLoginUser();
            SysUser user = loginUser.getUser();
            String userType = user.getUserType();

            if (!wxMaterialReceiveLogService.validateUserIdentity(userType)) {
                return R.fail("只有急救员、导师或弟子才能申请领取物料");
            }

            // 2. 调用Service处理申请逻辑
            String result = wxMaterialReceiveLogService.applyMaterial(userId, materialId);
            return R.ok(result);

        } catch (ServiceException e) {
            return R.fail(e.getMessage());
        } catch (Exception e) {
            logger.error("提交物料领取申请失败", e);
            return R.fail("申请提交失败，请稍后重试");
        }
    }

    /**
     * 用户查看自己的领取记录
     *
     * @return 领取记录列表
     */
    @GetMapping("/list")
    public R<List<WxMaterialReceiveLog>> getMaterialReceiveList() {
        try {
            Long userId = SecurityUtils.getUserId();

            // 调用Service查询用户的领取记录
            List<WxMaterialReceiveLog> wxReceiveLogList = wxMaterialReceiveLogService.selectUserMaterialReceiveList(userId);

            return R.ok(wxReceiveLogList);

        } catch (Exception e) {
            logger.error("查询物料领取记录失败", e);
            return R.fail("查询失败，请稍后重试");
        }
    }
}
