package com.drxin.wechat.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.drxin.wechat.domain.WxMaterialReceiveLog;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 微信物料领取记录Mapper接口
 *
 * <AUTHOR>
 * @date 2025-07-27
 */
public interface WxMaterialReceiveLogMapper extends BaseMapper<WxMaterialReceiveLog> {

    /**
     * 查询用户的物料领取记录列表
     * 
     * @param userId 用户ID
     * @return 用户的领取记录列表
     */
    List<WxMaterialReceiveLog> selectUserMaterialReceiveList(@Param("userId") Long userId);





    /**
     * 统计指定物料ID的数量（检查物料是否存在）
     * 
     * @param materialId 物料ID
     * @return 物料数量
     */
    int countMaterialById(@Param("materialId") Long materialId);
}
