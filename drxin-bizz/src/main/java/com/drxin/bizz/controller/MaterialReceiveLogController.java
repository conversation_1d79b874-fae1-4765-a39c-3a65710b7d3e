package com.drxin.bizz.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import com.drxin.common.annotation.Log;
import com.drxin.common.core.controller.BaseController;
import com.drxin.common.core.domain.AjaxResult;
import com.drxin.common.enums.BusinessType;
import com.drxin.bizz.domain.MaterialReceiveLog;
import com.drxin.bizz.service.IMaterialReceiveLogService;
import com.drxin.common.utils.poi.ExcelUtil;
import com.drxin.common.core.page.TableDataInfo;

/**
 * 物料领取记录Controller
 * 
 * <AUTHOR>
 * @date 2025-07-27
 */
@RestController
@RequestMapping("/bizz/material_receive_log")
public class MaterialReceiveLogController extends BaseController {
    @Autowired
    private IMaterialReceiveLogService materialReceiveLogService;

    /**
     * 查询物料领取记录列表
     */
    @PreAuthorize("@ss.hasPermi('bizz:material_receive_log:list')")
    @GetMapping("/list")
    public TableDataInfo list(MaterialReceiveLog materialReceiveLog){
        startPage();
        List<MaterialReceiveLog> list = materialReceiveLogService.selectMaterialReceiveLogList(materialReceiveLog);
        return getDataTable(list);
    }

    /**
     * 导出物料领取记录列表
     */
    @PreAuthorize("@ss.hasPermi('bizz:material_receive_log:export')")
    @Log(title = "物料领取记录", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, MaterialReceiveLog materialReceiveLog) {
        List<MaterialReceiveLog> list = materialReceiveLogService.selectMaterialReceiveLogList(materialReceiveLog);
        ExcelUtil<MaterialReceiveLog> util = new ExcelUtil<MaterialReceiveLog>(MaterialReceiveLog.class);
        util.exportExcel(response, list, "物料领取记录数据");
    }

    /**
     * 获取物料领取记录详细信息
     */
    @PreAuthorize("@ss.hasPermi('bizz:material_receive_log:query')")
    @GetMapping(value = "/{recordId}")
    public AjaxResult getInfo(@PathVariable("recordId") Long recordId) {
        return success(materialReceiveLogService.selectMaterialReceiveLogByRecordId(recordId));
    }

    /**
     * 新增物料领取记录
     */
    @PreAuthorize("@ss.hasPermi('bizz:material_receive_log:add')")
    @Log(title = "物料领取记录", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody MaterialReceiveLog materialReceiveLog) {
        return toAjax(materialReceiveLogService.insertMaterialReceiveLog(materialReceiveLog));
    }

    /**
     * 修改物料领取记录
     */
    @PreAuthorize("@ss.hasPermi('bizz:material_receive_log:edit')")
    @Log(title = "物料领取记录", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody MaterialReceiveLog materialReceiveLog) {
        return toAjax(materialReceiveLogService.updateMaterialReceiveLog(materialReceiveLog));
    }

    /**
     * 删除物料领取记录
     */
    @PreAuthorize("@ss.hasPermi('bizz:material_receive_log:remove')")
    @Log(title = "物料领取记录", businessType = BusinessType.DELETE)
	@DeleteMapping("/{recordIds}")
    public AjaxResult remove(@PathVariable Long[] recordIds) {
        return toAjax(materialReceiveLogService.deleteMaterialReceiveLogByRecordIds(recordIds));
    }

    /**
     * 管理员确认发放物料
     *
     * @param recordId 领取记录ID
     * @return 确认结果
     */
    @PreAuthorize("@ss.hasPermi('bizz:material_receive_log:edit')")
    @Log(title = "物料领取记录", businessType = BusinessType.UPDATE)
    @PostMapping("/confirm")
    public AjaxResult confirmMaterial(@RequestParam Long recordId) {
        try {
            // 调用Service处理确认逻辑
            String result = materialReceiveLogService.confirmMaterial(
                recordId,
                getLoginUser().getUserId(),
                getLoginUser().getUser().getNickName()
            );
            return AjaxResult.success(result);

        } catch (Exception e) {
            logger.error("确认发放物料失败", e);
            return AjaxResult.error(e.getMessage());
        }
    }
}
