package com.drxin.bizz.domain;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.drxin.common.annotation.Excel;
import com.drxin.common.core.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import lombok.experimental.Accessors;
/**
 * 物料信息对象 material_info
 * 
 * <AUTHOR>
 * @date 2025-07-27
 */
@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
public class MaterialInfo extends BaseEntity{
    private static final long serialVersionUID = 1L;

    /** 物料ID */
    private Long materialId;

    /** 物料名称 */
    @Excel(name = "物料名称")
    private String materialName;

    /** 物料用途说明 */
    @Excel(name = "物料用途说明")
    private String materialDesc;

    /** 使用方法说明 */
    @Excel(name = "使用方法说明")
    private String instruction;

    /** 库存总数 */
    @Excel(name = "库存总数")
    private Long totalQuantity;

    /** 是否首次免费领取（1是，0否） */
    @Excel(name = "是否首次免费领取", readConverterExp = "1=是，0否")
    private String firstFreeFlag;

    /** 允许首免的角色（逗号分隔，例如：急救员,急救导师,弟子） */
    @Excel(name = "允许首免的角色", readConverterExp = "逗=号分隔，例如：急救员,急救导师,弟子")
    private String freeRoles;

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("materialId", getMaterialId())
            .append("materialName", getMaterialName())
            .append("materialDesc", getMaterialDesc())
            .append("instruction", getInstruction())
            .append("totalQuantity", getTotalQuantity())
            .append("firstFreeFlag", getFirstFreeFlag())
            .append("freeRoles", getFreeRoles())
            .append("createTime", getCreateTime())
            .append("updateTime", getUpdateTime())
            .toString();
    }
}
