package com.drxin.bizz.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import java.util.List;
import com.drxin.common.utils.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.drxin.bizz.mapper.MaterialInfoMapper;
import com.drxin.bizz.domain.MaterialInfo;
import com.drxin.bizz.service.IMaterialInfoService;

/**
 * 物料信息Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-07-27
 */
@Service
public class MaterialInfoServiceImpl extends ServiceImpl<MaterialInfoMapper, MaterialInfo>  implements IMaterialInfoService {
    @Autowired
    private MaterialInfoMapper materialInfoMapper;

    /**
     * 查询物料信息
     * 
     * @param materialId 物料信息主键
     * @return 物料信息
     */
    @Override
    public MaterialInfo selectMaterialInfoByMaterialId(Long materialId) {
        return materialInfoMapper.selectMaterialInfoByMaterialId(materialId);
    }

    /**
     * 查询物料信息列表
     * 
     * @param materialInfo 物料信息
     * @return 物料信息
     */
    @Override
    public List<MaterialInfo> selectMaterialInfoList(MaterialInfo materialInfo) {
        return materialInfoMapper.selectMaterialInfoList(materialInfo);
    }

    /**
     * 新增物料信息
     * 
     * @param materialInfo 物料信息
     * @return 结果
     */
    @Override
    public int insertMaterialInfo(MaterialInfo materialInfo) {
        materialInfo.setCreateTime(DateUtils.getNowDate());
        return materialInfoMapper.insertMaterialInfo(materialInfo);
    }

    /**
     * 修改物料信息
     * 
     * @param materialInfo 物料信息
     * @return 结果
     */
    @Override
    public int updateMaterialInfo(MaterialInfo materialInfo) {
        materialInfo.setUpdateTime(DateUtils.getNowDate());
        return materialInfoMapper.updateMaterialInfo(materialInfo);
    }

    /**
     * 批量删除物料信息
     * 
     * @param materialIds 需要删除的物料信息主键
     * @return 结果
     */
    @Override
    public int deleteMaterialInfoByMaterialIds(Long[] materialIds) {
        return materialInfoMapper.deleteMaterialInfoByMaterialIds(materialIds);
    }

    /**
     * 删除物料信息信息
     * 
     * @param materialId 物料信息主键
     * @return 结果
     */
    @Override
    public int deleteMaterialInfoByMaterialId(Long materialId) {
        return materialInfoMapper.deleteMaterialInfoByMaterialId(materialId);
    }
}
