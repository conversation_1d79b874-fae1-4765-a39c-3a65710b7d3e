package com.drxin.bizz.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import java.util.Date;
import java.util.List;
import com.drxin.common.exception.ServiceException;
import com.drxin.common.utils.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.drxin.bizz.mapper.MaterialReceiveLogMapper;
import com.drxin.bizz.domain.MaterialReceiveLog;
import com.drxin.bizz.service.IMaterialReceiveLogService;

/**
 * 物料领取记录Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-07-27
 */
@Service
public class MaterialReceiveLogServiceImpl extends ServiceImpl<MaterialReceiveLogMapper, MaterialReceiveLog>  implements IMaterialReceiveLogService {
    @Autowired
    private MaterialReceiveLogMapper materialReceiveLogMapper;

    /**
     * 查询物料领取记录
     * 
     * @param recordId 物料领取记录主键
     * @return 物料领取记录
     */
    @Override
    public MaterialReceiveLog selectMaterialReceiveLogByRecordId(Long recordId) {
        return materialReceiveLogMapper.selectMaterialReceiveLogByRecordId(recordId);
    }

    /**
     * 查询物料领取记录列表
     * 
     * @param materialReceiveLog 物料领取记录
     * @return 物料领取记录
     */
    @Override
    public List<MaterialReceiveLog> selectMaterialReceiveLogList(MaterialReceiveLog materialReceiveLog) {
        return materialReceiveLogMapper.selectMaterialReceiveLogList(materialReceiveLog);
    }

    /**
     * 新增物料领取记录
     * 
     * @param materialReceiveLog 物料领取记录
     * @return 结果
     */
    @Override
    public int insertMaterialReceiveLog(MaterialReceiveLog materialReceiveLog) {
        materialReceiveLog.setCreateTime(DateUtils.getNowDate());
        return materialReceiveLogMapper.insertMaterialReceiveLog(materialReceiveLog);
    }

    /**
     * 修改物料领取记录
     * 
     * @param materialReceiveLog 物料领取记录
     * @return 结果
     */
    @Override
    public int updateMaterialReceiveLog(MaterialReceiveLog materialReceiveLog) {
        materialReceiveLog.setUpdateTime(DateUtils.getNowDate());
        return materialReceiveLogMapper.updateMaterialReceiveLog(materialReceiveLog);
    }

    /**
     * 批量删除物料领取记录
     * 
     * @param recordIds 需要删除的物料领取记录主键
     * @return 结果
     */
    @Override
    public int deleteMaterialReceiveLogByRecordIds(Long[] recordIds) {
        return materialReceiveLogMapper.deleteMaterialReceiveLogByRecordIds(recordIds);
    }

    /**
     * 删除物料领取记录信息
     *
     * @param recordId 物料领取记录主键
     * @return 结果
     */
    @Override
    public int deleteMaterialReceiveLogByRecordId(Long recordId) {
        return materialReceiveLogMapper.deleteMaterialReceiveLogByRecordId(recordId);
    }



    /**
     * 管理员确认发放物料
     *
     * @param recordId 领取记录ID
     * @param handlerId 操作人ID
     * @param handlerName 操作人姓名
     * @return 确认结果消息
     */
    @Override
    public String confirmMaterial(Long recordId, Long handlerId, String handlerName) {
        // 1. 查询记录是否存在
        MaterialReceiveLog receiveLog = this.selectMaterialReceiveLogByRecordId(recordId);
        if (receiveLog == null) {
            throw new ServiceException("领取记录不存在");
        }

        // 2. 校验当前状态是否为申请中
        if (!"applying".equals(receiveLog.getStatus())) {
            throw new ServiceException("只能确认申请中的记录");
        }

        // 3. 更新状态为已领取，记录确认时间和操作人
        receiveLog.setStatus("completed");
        receiveLog.setConfirmTime(new Date());
        receiveLog.setHandlerId(handlerId);
        receiveLog.setHandlerName(handlerName);

        int result = this.updateMaterialReceiveLog(receiveLog);

        if (result <= 0) {
            throw new ServiceException("确认发放失败");
        }

        return "确认发放成功";
    }


}
