package com.drxin.bizz.service;

import com.baomidou.mybatisplus.extension.service.IService;
import java.util.List;
import com.drxin.bizz.domain.MaterialInfo;

/**
 * 物料信息Service接口
 * 
 * <AUTHOR>
 * @date 2025-07-27
 */
public interface IMaterialInfoService extends IService<MaterialInfo> {

    /**
     * 查询物料信息
     * 
     * @param materialId 物料信息主键
     * @return 物料信息
     */
    public MaterialInfo selectMaterialInfoByMaterialId(Long materialId);

    /**
     * 查询物料信息列表
     * 
     * @param materialInfo 物料信息
     * @return 物料信息集合
     */
    public List<MaterialInfo> selectMaterialInfoList(MaterialInfo materialInfo);

    /**
     * 新增物料信息
     * 
     * @param materialInfo 物料信息
     * @return 结果
     */
    public int insertMaterialInfo(MaterialInfo materialInfo);

    /**
     * 修改物料信息
     * 
     * @param materialInfo 物料信息
     * @return 结果
     */
    public int updateMaterialInfo(MaterialInfo materialInfo);

    /**
     * 批量删除物料信息
     * 
     * @param materialIds 需要删除的物料信息主键集合
     * @return 结果
     */
    public int deleteMaterialInfoByMaterialIds(Long[] materialIds);

    /**
     * 删除物料信息信息
     * 
     * @param materialId 物料信息主键
     * @return 结果
     */
    public int deleteMaterialInfoByMaterialId(Long materialId);
}
