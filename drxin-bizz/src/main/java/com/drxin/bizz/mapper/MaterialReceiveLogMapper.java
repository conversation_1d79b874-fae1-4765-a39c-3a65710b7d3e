package com.drxin.bizz.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import java.util.List;
import com.drxin.bizz.domain.MaterialReceiveLog;

/**
 * 物料领取记录Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-07-27
 */
public interface MaterialReceiveLogMapper extends BaseMapper<MaterialReceiveLog> {
    /**
     * 查询物料领取记录
     * 
     * @param recordId 物料领取记录主键
     * @return 物料领取记录
     */
    public MaterialReceiveLog selectMaterialReceiveLogByRecordId(Long recordId);

    /**
     * 查询物料领取记录列表
     * 
     * @param materialReceiveLog 物料领取记录
     * @return 物料领取记录集合
     */
    public List<MaterialReceiveLog> selectMaterialReceiveLogList(MaterialReceiveLog materialReceiveLog);

    /**
     * 新增物料领取记录
     * 
     * @param materialReceiveLog 物料领取记录
     * @return 结果
     */
    public int insertMaterialReceiveLog(MaterialReceiveLog materialReceiveLog);

    /**
     * 修改物料领取记录
     * 
     * @param materialReceiveLog 物料领取记录
     * @return 结果
     */
    public int updateMaterialReceiveLog(MaterialReceiveLog materialReceiveLog);

    /**
     * 删除物料领取记录
     * 
     * @param recordId 物料领取记录主键
     * @return 结果
     */
    public int deleteMaterialReceiveLogByRecordId(Long recordId);

    /**
     * 批量删除物料领取记录
     * 
     * @param recordIds 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteMaterialReceiveLogByRecordIds(Long[] recordIds);
}
