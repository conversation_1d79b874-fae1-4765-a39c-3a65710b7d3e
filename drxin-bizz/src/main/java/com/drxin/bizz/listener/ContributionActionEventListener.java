package com.drxin.bizz.listener;

import com.drxin.bizz.factory.ContributionStrategyFactory;
import com.drxin.bizz.service.IContributionEventLogService;
import com.drxin.bizz.strategy.ContributionStrategy;
import com.drxin.framework.event.ContributionActionEvent;

import lombok.extern.slf4j.Slf4j;
import org.springframework.context.event.EventListener;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;


/**
 * 业务行为事件监听器（统一入口）
 * 处理各种业务行为事件，包括贡献值计算、物料发放等
 * 设计原则：
 * - 基于Spring事件机制实现异步处理
 * - 监听器只负责找到策略并调用
 * - 策略实现类负责完整的处理逻辑（计算+记录日志）
 * - 不关心单个还是批量，由策略内部处理
 *
 * <AUTHOR>
 * @date 2025-07-10
 */
@Slf4j
@Component
public class ContributionActionEventListener {

    @Resource
    private ContributionStrategyFactory strategyFactory;

    @Resource
    private IContributionEventLogService contributionEventLogService;

    /**
     * 异步处理贡献值事件
     *
     * @param event 贡献值行为事件
     */
    @Async
    @EventListener
    public void handle(ContributionActionEvent event) {
        Long eventLogId = null;
        try {
            String actionCode = event.getActionCode();
            log.info("接收到贡献值事件: userId={}, actionCode={}", event.getUserId(), actionCode);

            // 1. 记录事件日志
            eventLogId = contributionEventLogService.recordEventLog(event);

            // 2. 获取策略并调用处理方法
            ContributionStrategy strategy = strategyFactory.getStrategy(actionCode);
            if (strategy == null) {
                String message = "未找到策略处理器: " + actionCode;
                log.warn(message);
                contributionEventLogService.updateEventFailed(eventLogId, message);
                return;
            }

            // 3. 策略负责完整的处理逻辑
            strategy.process(event);

            // 4. 更新事件处理状态为成功
            contributionEventLogService.updateEventSuccess(eventLogId, "策略处理成功");

            log.info("贡献值事件处理完成: userId={}, actionCode={}, eventLogId={}",
                event.getUserId(), actionCode, eventLogId);

        } catch (Exception e) {
            String errorMessage = "处理贡献值事件失败: " + e.getMessage();
            log.error("处理贡献值事件失败: userId={}, actionCode={}, eventLogId={}",
                event.getUserId(), event.getActionCode(), eventLogId, e);
            // 更新事件处理状态为失败
            contributionEventLogService.updateEventFailed(eventLogId, errorMessage);
        }
    }
}
